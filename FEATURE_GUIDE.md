# RSS 浏览器功能介绍

## 🎯 产品概述

RSS 浏览器是一款专为 uTools 用户打造的现代化 RSS 阅读器插件，采用 Obsidian 风格的深色主题设计，提供简洁高效的 RSS 订阅和阅读体验。无论您是技术爱好者、新闻读者还是内容创作者，都能在这里找到属于自己的信息获取方式。

## 🚀 快速启动

### 启动方式
- **关键词启动**：在 uTools 中输入 `RSS`、`订阅`、`新闻`、`博客` 等关键词
- **文本选择**：选中网页上的 RSS 链接，右键选择"订阅 RSS 源"
- **快捷访问**：添加到 uTools 常用功能，一键启动

## 📰 订阅管理

### RSS 源订阅
- **智能订阅**：只需输入 RSS 源 URL，系统自动解析并获取源信息
- **示例源推荐**：内置优质 RSS 源推荐，包括：
  - 阮一峰科技周刊
  - 爱范儿科技资讯
  - 联合早报国际新闻
- **订阅验证**：自动验证 RSS 源有效性，提供详细错误提示

### 文件夹管理
- **分类整理**：创建文件夹对订阅源进行分类管理
- **拖拽排序**：支持拖拽调整订阅源和文件夹的顺序
- **层级结构**：支持多层级文件夹嵌套，灵活组织内容
- **批量操作**：支持文件夹重命名、删除等批量管理操作

### 订阅源管理
- **重命名**：自定义订阅源显示名称
- **删除确认**：删除前显示确认对话框，防止误操作
- **状态显示**：实时显示未读文章数量和最后更新时间
- **图标显示**：自动获取并显示订阅源的 favicon

## 🔄 内容同步

### 智能同步
- **一键同步**：点击同步按钮，批量更新所有订阅源
- **增量更新**：只同步新增文章，避免重复数据
- **进度显示**：实时显示同步进度和当前处理的订阅源
- **错误处理**：同步失败时提供详细错误信息和重试机制

### 同步策略
- **自动去重**：基于文章 URL 和发布时间自动去除重复文章
- **数量限制**：每个订阅源最多保留 100 篇文章，保持数据库轻量
- **时间排序**：文章按发布时间倒序排列，最新文章优先显示

## 📖 文章阅读

### 文章列表
- **卡片式布局**：采用卡片式设计，信息层次清晰
- **关键信息**：显示文章标题、摘要、发布时间、作者信息
- **封面图片**：自动提取文章封面图，提升视觉体验
- **占位符设计**：无图片时显示彩色字母占位符

### 文章详情
- **富文本显示**：完整支持 HTML 格式，保持原文排版
- **代码高亮**：集成 Shiki 代码高亮引擎，支持多种编程语言
- **图片优化**：智能处理防盗链图片，确保图片正常显示
- **响应式设计**：适配不同屏幕尺寸，提供最佳阅读体验

### 阅读功能
- **字体调节**：支持小、中、大三种字体大小设置
- **返回顶部**：文章切换时自动回到顶部
- **原文链接**：一键跳转到文章原始页面
- **链接复制**：快速复制文章链接到剪贴板

## 🏷️ 标签与书签

### 书签系统
- **一键收藏**：点击星标按钮快速收藏重要文章
- **书签管理**：专门的书签页面，集中管理收藏内容
- **状态同步**：书签状态实时同步，跨页面保持一致

### 标签功能
- **自定义标签**：为文章添加个性化标签
- **标签搜索**：通过标签快速筛选相关文章
- **智能建议**：输入时提供标签自动完成建议
- **标签管理**：支持标签的添加、删除和编辑

## 🔍 搜索与过滤

### 全文搜索
- **多维搜索**：支持按标题、作者、标签、内容进行搜索
- **实时搜索**：输入时实时显示搜索结果
- **搜索高亮**：搜索结果中关键词高亮显示
- **搜索历史**：记录常用搜索词，提高搜索效率

### 内容过滤
- **按源过滤**：选择特定订阅源查看文章
- **按文件夹过滤**：查看文件夹下所有订阅源的文章
- **按标签过滤**：筛选包含特定标签的文章
- **时间过滤**：按发布时间范围筛选文章

## ⚙️ 系统设置

### 阅读设置
- **字体大小**：三档字体大小可选（小/中/大）
- **浏览器选择**：支持 uTools 内置浏览器和系统默认浏览器
- **阅读模式**：优化的阅读界面，减少干扰

### 缓存管理
- **缓存保留时间**：可设置缓存保留天数（7-90天）
- **自动清理**：支持自动清理过期缓存
- **手动清理**：一键清理所有缓存数据
- **清理模式**：支持"清空"和"删除"两种清理模式

### 数据管理
- **数据持久化**：使用 uTools 数据库安全存储数据
- **数据迁移**：自动处理版本升级时的数据迁移
- **备份恢复**：支持数据的导出和导入（开发中）

## 🎨 界面设计

### Obsidian 风格
- **深色主题**：护眼的深色界面，适合长时间阅读
- **简洁布局**：左侧导航 + 右侧内容的经典布局
- **统一设计**：遵循 Obsidian 设计语言，界面一致性强

### 交互体验
- **流畅动画**：平滑的过渡动画和交互效果
- **拖拽操作**：直观的拖拽排序功能
- **右键菜单**：丰富的上下文菜单操作
- **工具提示**：详细的操作提示和说明

### 响应式设计
- **自适应布局**：适配不同窗口大小
- **触控友好**：支持触控设备操作
- **键盘快捷键**：支持常用操作的键盘快捷键

## 🔧 技术特性

### 现代化架构
- **Vue 3 + TypeScript**：类型安全的现代前端框架
- **Pinia 状态管理**：响应式的状态管理解决方案
- **UnoCSS 样式**：高性能的原子化 CSS 框架

### 性能优化
- **懒加载**：图片和内容的懒加载机制
- **虚拟滚动**：大量数据时的性能优化
- **缓存策略**：智能缓存减少网络请求
- **代码分割**：按需加载，减少初始加载时间

## 🛡️ 安全与隐私

### 数据安全
- **本地存储**：所有数据存储在本地，不上传到服务器
- **加密存储**：敏感数据采用加密存储
- **权限控制**：严格的数据访问权限控制

### 隐私保护
- **无追踪**：不收集用户行为数据
- **离线使用**：支持离线阅读已缓存的文章
- **数据控制**：用户完全控制自己的数据

## 📱 使用技巧

### 高效阅读
1. 使用文件夹分类管理不同类型的订阅源
2. 利用书签功能收藏重要文章
3. 通过标签系统建立个人知识体系
4. 定期清理缓存保持应用流畅

### 订阅建议
1. 选择更新频率适中的优质源
2. 避免订阅过多相似内容的源
3. 定期检查和清理不活跃的订阅源
4. 利用文件夹功能进行主题分类

---

**开始您的 RSS 阅读之旅吧！** 📚✨
